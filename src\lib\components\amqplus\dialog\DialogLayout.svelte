<!-- DialogLayout.svelte - <PERSON>les dialog layout logic -->
<script>
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';

	let {
		open = $bindable(false),
		nodeData = $bindable(null),
		dialogSize = 'medium',
		isValid = true,
		getNodeColor = () => '#6366f1',
		onSave = () => {},
		onCancel = () => {},
		children
	} = $props();

	function handleSave() {
		if (!isValid || !nodeData) return;
		onSave();
		open = false;
	}

	function handleCancel() {
		onCancel();
		open = false;
	}
</script>

<Dialog {open} onOpenChange={(v) => open = v}>
	<!-- Apply a dynamic class based on the 'size' from config -->
	<DialogContent
		class="dialog-size-{dialogSize} {dialogSize === 'fullscreen' ? 'p-0 border-0' : ''} max-h-[90vh] overflow-y-auto"
		showCloseButton={false}
		portalProps={dialogSize === 'fullscreen' ? { class: 'fullscreen-portal' } : undefined}
	>

		<!-- RENDER FULLSCREEN LAYOUT -->
		{#if dialogSize === 'fullscreen'}
			<div class="fullscreen-container">
				<!-- Header -->
				<div class="fullscreen-header">
					<DialogHeader class="text-center">
						<DialogTitle class="flex items-center justify-center mb-2 text-3xl text-gray-900">
							<span class="mr-4 text-4xl" style="color: {getNodeColor()}">{nodeData?.icon}</span>
							Edit {nodeData?.title}
						</DialogTitle>
						<DialogDescription class="text-lg text-gray-600">
							Configure the settings for this node
						</DialogDescription>
					</DialogHeader>
				</div>

				<!-- Scrollable Content -->
				<div class="fullscreen-content">
					<div class="min-h-full p-8 space-y-8">
						{@render children()}
					</div>
				</div>

				<!-- Footer -->
				<div class="fullscreen-footer">
					<DialogFooter class="flex justify-center gap-4">
						<Button variant="outline" onclick={handleCancel} class="px-8 py-3 text-lg">Cancel</Button>
						<Button onclick={handleSave} disabled={!isValid} class="px-8 py-3 text-lg hover:opacity-90 focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 transition-all" style="background-color: {getNodeColor()}; focus-ring-color: {getNodeColor()};">Save Changes</Button>
					</DialogFooter>
				</div>
			</div>

		<!-- RENDER STANDARD (small, medium, large) LAYOUT -->
		{:else}
			<DialogHeader class="p-6 pb-4">
				<DialogTitle class="flex items-center text-xl">
					<span class="mr-3 text-2xl" style="color: {getNodeColor()}">{nodeData?.icon}</span>
					Edit {nodeData?.title}
				</DialogTitle>
				<DialogDescription class="pt-1 text-base">
					Configure the settings for this node.
				</DialogDescription>
			</DialogHeader>

			<div class="p-6 pt-0 overflow-y-auto max-h-[60vh]">
				{@render children()}
			</div>

			<DialogFooter class="p-6 pt-4">
				<Button variant="outline" onclick={handleCancel}>Cancel</Button>
				<Button onclick={handleSave} disabled={!isValid} class="hover:opacity-90 focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50 transition-all" style="background-color: {getNodeColor()}; focus-ring-color: {getNodeColor()};">Save</Button>
			</DialogFooter>
		{/if}

	</DialogContent>
</Dialog>

<style>
	/* Dialog size classes */
	:global(.dialog-size-small) {
		max-width: min(400px, 90vw);
		max-height: 90vh;
	}

	:global(.dialog-size-medium) {
		max-width: min(600px, 90vw);
		max-height: 90vh;
	}

	:global(.dialog-size-large) {
		max-width: min(800px, 90vw);
		max-height: 90vh;
	}

	:global(.dialog-size-fullscreen) {
		width: 100vw;
		height: 100vh;
		max-width: none;
		max-height: none;
		margin: 0;
		border-radius: 0;
	}

	/* Mobile responsive adjustments */
	@media (max-width: 640px) {
		:global(.dialog-size-small),
		:global(.dialog-size-medium),
		:global(.dialog-size-large) {
			max-width: 95vw;
			max-height: 85vh;
			margin: 1rem;
		}

		.fullscreen-container {
			border-width: 4px;
			border-radius: 8px;
		}

		.fullscreen-header {
			padding: 1rem;
		}

		.fullscreen-content .min-h-full {
			padding: 1rem;
		}

		.fullscreen-footer {
			padding: 1rem;
		}
	}

	/* Fullscreen layout styles */
	.fullscreen-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		border: 8px solid #e5e7eb;
		border-radius: 12px;
		background: white;
		margin: 0;
	}

	.fullscreen-header {
		flex-shrink: 0;
		padding: 2rem;
		border-bottom: 1px solid #e5e7eb;
		background: #f9fafb;
	}

	.fullscreen-content {
		flex: 1;
		overflow-y: auto;
		background: #f9fafb;
	}

	.fullscreen-footer {
		flex-shrink: 0;
		padding: 1.5rem 2rem;
		border-top: 1px solid #e5e7eb;
		background: white;
	}

	:global(.fullscreen-portal) {
		position: fixed;
		inset: 0;
		z-index: 50;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(0, 0, 0, 0.8);
		padding: 1rem;
	}
</style>
