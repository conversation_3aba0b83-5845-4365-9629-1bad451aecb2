<!-- SongTypesSelection.svelte - Complex song types and selection form -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import RangeSlider from 'svelte-range-slider-pips';

	let {
		editedValue = $bindable(),
		config,
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20
	} = $props();
</script>

<div class="space-y-8">
	<div class="text-center">
		<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
		<p class="mt-2 text-gray-600">Configure song types and selection preferences</p>
	</div>

	<!-- Mode Switch -->
	<div class="flex items-center justify-center p-4 space-x-4 bg-white border border-gray-200 rounded-lg shadow-sm">
		<Label class="text-lg font-medium text-gray-700">Mode:</Label>
		<div class="flex items-center space-x-3">
			<Label for="mode-switch" class="text-sm {editedValue.mode === 'percentage' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Percentage</Label>
			<Switch
				id="mode-switch"
				checked={editedValue.mode === 'count'}
				onCheckedChange={(checked) => editedValue.mode = checked ? 'count' : 'percentage'}
			/>
			<Label for="mode-switch" class="text-sm {editedValue.mode === 'count' ? 'font-semibold text-blue-600' : 'text-gray-500'}">Exact Count</Label>
		</div>
		<div class="text-xs text-gray-500">
			Total Songs: {getTotalSongs()}
		</div>
	</div>

	<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
		<!-- Song Types Section -->
		<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-4 text-lg font-semibold text-gray-800">Song Types</Label>
			<div class="space-y-6">
				<!-- Openings -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.openings.enabled} id="openings" />
						<Label for="openings" class="text-sm">Openings</Label>
					</div>
					{#if editedValue.songTypes.openings.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.openings.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => editedValue.songTypes.openings.percentage = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.openings.percentage}%</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.openings.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => editedValue.songTypes.openings.count = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.openings.count} songs</div>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Endings -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.endings.enabled} id="endings" />
						<Label for="endings" class="text-sm">Endings</Label>
					</div>
					{#if editedValue.songTypes.endings.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.endings.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => editedValue.songTypes.endings.percentage = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.endings.percentage}%</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.endings.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => editedValue.songTypes.endings.count = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.endings.count} songs</div>
							</div>
						{/if}
					{/if}
				</div>

				<!-- Inserts -->
				<div class="space-y-2">
					<div class="flex items-center space-x-2">
						<Checkbox bind:checked={editedValue.songTypes.inserts.enabled} id="inserts" />
						<Label for="inserts" class="text-sm">Inserts</Label>
					</div>
					{#if editedValue.songTypes.inserts.enabled}
						{#if editedValue.mode === 'percentage'}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.inserts.percentage]}
									min={0} max={100} step={1} pips pipstep={25} all="label"
									on:change={(e) => editedValue.songTypes.inserts.percentage = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.inserts.percentage}%</div>
							</div>
						{:else}
							<div class="px-2">
								<RangeSlider
									values={[editedValue.songTypes.inserts.count]}
									min={0} max={getTotalSongs()} step={1} pips pipstep={Math.max(1, Math.floor(getTotalSongs() / 4))} all="label"
									on:change={(e) => editedValue.songTypes.inserts.count = e.detail.value}
									--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
								/>
								<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.inserts.count} songs</div>
							</div>
						{/if}
					{/if}
				</div>
			</div>
		</div>

		<!-- Song Selection Section -->
		<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
			<Label class="block mb-4 text-lg font-semibold text-gray-800">Song Selection</Label>
			<div class="space-y-6">
				<!-- Random -->
				<div class="space-y-2">
					<Label class="text-sm">Random</Label>
					<div class="px-2">
						<RangeSlider
							values={[editedValue.songSelection.random]}
							min={0} max={100} step={1} pips pipstep={25} all="label"
							on:change={(e) => editedValue.songSelection.random = e.detail.value}
							--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
						/>
						<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.random}%</div>
					</div>
				</div>

				<!-- Mix -->
				<div class="space-y-2">
					<Label class="text-sm">Mix</Label>
					<div class="px-2">
						<RangeSlider
							values={[editedValue.songSelection.mix]}
							min={0} max={100} step={1} pips pipstep={25} all="label"
							on:change={(e) => editedValue.songSelection.mix = e.detail.value}
							--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
						/>
						<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.mix}%</div>
					</div>
				</div>

				<!-- Watched -->
				<div class="space-y-2">
					<Label class="text-sm">Watched</Label>
					<div class="px-2">
						<RangeSlider
							values={[editedValue.songSelection.watched]}
							min={0} max={100} step={1} pips pipstep={25} all="label"
							on:change={(e) => editedValue.songSelection.watched = e.detail.value}
							--slider={getNodeColor()} --handle={getNodeColor()} --range={getNodeColor()} --progress={getNodeColor()}
						/>
						<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.watched}%</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
