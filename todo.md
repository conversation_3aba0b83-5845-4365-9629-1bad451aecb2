Implement the logic for song difficulty choosing after we;ve delat with other problems. Same fir abune score, player score.
- Quiz 3rd zone
  - <PERSON> Diff<PERSON>ulty (Easy/Medium/Hard; def: all enabled in basic view, in advanced view full on editor with possibility to set perctanges for each difficulty range or the static number of songs from each difficulty range)
- Anime 4th zone
  - Player score (1-10; def: full range) [with popout window to edit this setting, enabling percentage split based on player score and to disallow certain scores if perctantage mode is not used. If percentange mode is not used then full range should be considered.]
  - Anime Score (2-10; def: full range) [with popout window to edit this setting, enabling percentage split based on player score and to disallow certain scores if perctantage mode is not used. If percentange mode is not used then full range should be considered.]
  - Vintage (from Winter 1944 to Fall 2025) [with poput window to freely edit the conditions. User should be able to select a range from the songs will play. The valid ranges are WINTER, SPRING, SUMMER, FALL and the years from 1944 to 2025. The user can also add multiple conditions like SUMMER 2000 - FALL 2001 AND WINTER 2020 - SPRING 2024]
- Genres (list of genres to include/exclude/optionally included in basic view, in advanced view full on editor with possibility to set perctanges for each genre or the static number of songs from each genre)
- Tags (list of tags to include/exclude/optionally included in basic view, in advanced view full on editor with possibility to set perctanges for each tag or the static number of songs from each tag)
  
Make the guess time able to be random in a range
Make the extra guess time able to be random in a range
Make the playback speed be random from the allowed values
But all of the above settings now resolve to {object Object] fix that.

Make only one container scrollable inside the modal. The inner container "Sample Point(%)" Should be scrollable with the outer container "Edit Sample Point" 
Make all nodes have more space vertically to display more info.

Inside the Edit Song Types & Selection modal
The Sliders in Song Type should be linked with each other, and always giving the total of the MAX SONGS or 100% percentage. Make the now displayer of the value an input box with editable number. The only exception when you do not need to automatically check and validate is when manually editing the value inside the input box. In that case validate on leaving the menu and show an error if the validation fails (do it also when user does not interact with the input box - in short do it anyways always)