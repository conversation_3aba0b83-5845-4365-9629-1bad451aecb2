<!-- ComplexFormFields.svelte - Handles complex form field types -->
<script>
	import { Label } from '$lib/components/ui/label';
	import { Switch } from '$lib/components/ui/switch';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import RangeSlider from 'svelte-range-slider-pips';
	import SongTypesSelection from './complex/SongTypesSelection.svelte';
	import SongsAndTypesSelection from './complex/SongsAndTypesSelection.svelte';

	import SongDifficulty from './complex/SongDifficulty.svelte';
	import ScoreRange from './complex/ScoreRange.svelte';
	import Vintage from './complex/Vintage.svelte';
	import GenresTags from './complex/GenresTags.svelte';

	let {
		config,
		editedValue = $bindable(),
		isValid = $bindable(true),
		validationMessage = $bindable(''),
		getNodeColor = () => '#6366f1',
		getTotalSongs = () => 20
	} = $props();

	// Real-time validation and auto-adjustment for song types
	$effect(() => {
		if (editedValue && (config?.type === 'complex-song-types-selection' || config?.type === 'complex-songs-and-types')) {
			const totalSongs = config?.type === 'complex-songs-and-types' ? (editedValue.songCount || 20) : getTotalSongs();

			if (editedValue.mode === 'percentage') {
				// Auto-adjust percentages to not exceed 100%
				const songTypes = editedValue.songTypes;
				const enabledTypes = Object.keys(songTypes).filter(type => songTypes[type].enabled);

				if (enabledTypes.length > 0) {
					let totalPercentage = enabledTypes.reduce((sum, type) => sum + songTypes[type].percentage, 0);

					if (totalPercentage > 100) {
						// Proportionally reduce percentages to fit within 100%
						const scaleFactor = 100 / totalPercentage;
						enabledTypes.forEach(type => {
							songTypes[type].percentage = Math.round(songTypes[type].percentage * scaleFactor);
						});
					}
				}

				// Auto-adjust song selection percentages
				const selection = editedValue.songSelection;
				const selectionTotal = selection.random + selection.mix + selection.watched;
				if (selectionTotal > 100) {
					const scaleFactor = 100 / selectionTotal;
					selection.random = Math.round(selection.random * scaleFactor);
					selection.mix = Math.round(selection.mix * scaleFactor);
					selection.watched = Math.round(selection.watched * scaleFactor);
				}
			} else if (editedValue.mode === 'count') {
				// Auto-adjust counts to not exceed total songs
				const songTypes = editedValue.songTypes;
				const enabledTypes = Object.keys(songTypes).filter(type => songTypes[type].enabled);

				if (enabledTypes.length > 0) {
					let totalCount = enabledTypes.reduce((sum, type) => sum + songTypes[type].count, 0);

					if (totalCount > totalSongs) {
						// Proportionally reduce counts to fit within total songs
						const scaleFactor = totalSongs / totalCount;
						enabledTypes.forEach(type => {
							songTypes[type].count = Math.max(1, Math.floor(songTypes[type].count * scaleFactor));
						});
					}
				}
			}
		}
	});
</script>

{#if config.type === 'complex-songs-and-types'}
	<SongsAndTypesSelection
		bind:editedValue
		{config}
		{getNodeColor}
		bind:isValid
		bind:validationMessage
	/>

{:else if config.type === 'complex-song-types-selection'}
	<SongTypesSelection
		bind:editedValue
		{config}
		{getNodeColor}
		{getTotalSongs}
	/>

{:else if config.type === 'complex-song-difficulty'}
	<SongDifficulty 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else if config.type === 'complex-score-range'}
	<ScoreRange 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else if config.type === 'complex-vintage'}
	<Vintage 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else if config.type === 'complex-genres-tags'}
	<GenresTags 
		bind:editedValue 
		{config} 
		{getNodeColor}
	/>

{:else}
	<div class="p-8 text-center text-gray-500">
		<p>Complex form type "{config.type}" not yet implemented.</p>
		<p class="text-sm mt-2">This will be a placeholder until the specific form is created.</p>
	</div>
{/if}
